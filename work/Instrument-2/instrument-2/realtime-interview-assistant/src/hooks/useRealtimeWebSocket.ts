'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { RealtimeWebSocketManager } from '@/lib/websocket-manager';
import { ConnectionState, RealtimeEvent, RealtimeSession } from '@/types/realtime';

interface UseRealtimeWebSocketOptions {
  apiKey: string;
  autoConnect?: boolean;
  sessionConfig?: Partial<RealtimeSession>;
}

interface UseRealtimeWebSocketReturn {
  connectionState: ConnectionState;
  connect: () => Promise<void>;
  disconnect: () => void;
  sendEvent: (event: RealtimeEvent) => void;
  updateSession: (session: Partial<RealtimeSession>) => void;
  appendAudioBuffer: (audioData: string) => void;
  commitAudioBuffer: () => void;
  clearAudioBuffer: () => void;
  createResponse: (instructions?: string) => void;
  cancelResponse: () => void;
  addEventListener: (eventType: string, listener: (event: RealtimeEvent) => void) => void;
  removeEventListener: (eventType: string, listener: (event: RealtimeEvent) => void) => void;
  isSessionExpired: () => boolean;
  reconnectWithHistory: () => Promise<void>;
}

export function useRealtimeWebSocket({
  apiKey,
  autoConnect = false,
  sessionConfig
}: UseRealtimeWebSocketOptions): UseRealtimeWebSocketReturn {
  const [connectionState, setConnectionState] = useState<ConnectionState>(ConnectionState.DISCONNECTED);
  const managerRef = useRef<RealtimeWebSocketManager | null>(null);
  const isInitializedRef = useRef(false);

  // Initialize manager
  useEffect(() => {
    if (!apiKey || isInitializedRef.current) return;

    managerRef.current = new RealtimeWebSocketManager(apiKey);
    isInitializedRef.current = true;

    // Listen for connection state changes
    const handleConnectionStateChange = (event: RealtimeEvent) => {
      if (event.type === 'connection.state_changed') {
        setConnectionState(event.state as ConnectionState);
      }
    };

    managerRef.current.addEventListener('connection.state_changed', handleConnectionStateChange);

    return () => {
      if (managerRef.current) {
        managerRef.current.removeEventListener('connection.state_changed', handleConnectionStateChange);
        managerRef.current.disconnect();
      }
    };
  }, [apiKey]);

  const connect = useCallback(async () => {
    if (!managerRef.current) {
      throw new Error('WebSocket manager not initialized');
    }

    try {
      await managerRef.current.connect();
    } catch (error) {
      console.error('Failed to connect:', error);
      throw error;
    }
  }, []);

  // Auto-connect if enabled
  useEffect(() => {
    if (autoConnect && managerRef.current && connectionState === ConnectionState.DISCONNECTED) {
      connect();
    }
  }, [autoConnect, connectionState, connect]);

  // Apply session configuration - only after connection is established and stable
  useEffect(() => {
    if (sessionConfig && managerRef.current && connectionState === ConnectionState.CONNECTED) {
      // Add a small delay to ensure WebSocket is fully ready
      const timer = setTimeout(() => {
        if (managerRef.current && connectionState === ConnectionState.CONNECTED) {
          managerRef.current.updateSession(sessionConfig);
        }
      }, 200);

      return () => clearTimeout(timer);
    }
  }, [sessionConfig, connectionState]);

  const disconnect = useCallback(() => {
    if (managerRef.current) {
      managerRef.current.disconnect();
    }
  }, []);

  const sendEvent = useCallback((event: RealtimeEvent) => {
    if (managerRef.current) {
      managerRef.current.sendEvent(event);
    }
  }, []);

  const updateSession = useCallback((session: Partial<RealtimeSession>) => {
    if (managerRef.current) {
      managerRef.current.updateSession(session);
    }
  }, []);

  const appendAudioBuffer = useCallback((audioData: string) => {
    if (managerRef.current) {
      managerRef.current.appendAudioBuffer(audioData);
    }
  }, []);

  const commitAudioBuffer = useCallback(() => {
    if (managerRef.current) {
      managerRef.current.commitAudioBuffer();
    }
  }, []);

  const clearAudioBuffer = useCallback(() => {
    if (managerRef.current) {
      managerRef.current.clearAudioBuffer();
    }
  }, []);

  const createResponse = useCallback((instructions?: string) => {
    if (managerRef.current) {
      managerRef.current.createResponse(instructions);
    }
  }, []);

  const cancelResponse = useCallback(() => {
    if (managerRef.current) {
      managerRef.current.cancelResponse();
    }
  }, []);

  const addEventListener = useCallback((eventType: string, listener: (event: RealtimeEvent) => void) => {
    if (managerRef.current) {
      managerRef.current.addEventListener(eventType, listener);
    }
  }, []);

  const removeEventListener = useCallback((eventType: string, listener: (event: RealtimeEvent) => void) => {
    if (managerRef.current) {
      managerRef.current.removeEventListener(eventType, listener);
    }
  }, []);

  const isSessionExpired = useCallback(() => {
    return managerRef.current ? managerRef.current.isSessionExpired() : false;
  }, []);

  const reconnectWithHistory = useCallback(async () => {
    if (managerRef.current) {
      await managerRef.current.reconnectWithHistory();
    }
  }, []);

  return {
    connectionState,
    connect,
    disconnect,
    sendEvent,
    updateSession,
    appendAudioBuffer,
    commitAudioBuffer,
    clearAudioBuffer,
    createResponse,
    cancelResponse,
    addEventListener,
    removeEventListener,
    isSessionExpired,
    reconnectWithHistory
  };
}
