{"name": "realtime-interview-assistant", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@ai-sdk/openai": "^2.0.23", "@assistant-ui/react": "^0.10.45", "@assistant-ui/react-ai-sdk": "^1.0.5", "@assistant-ui/react-markdown": "^0.10.9", "@assistant-ui/styles": "^0.1.18", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.8", "@ricky0123/vad-node": "^0.0.3", "@ricky0123/vad-web": "^0.0.26", "@types/ws": "^8.18.1", "ai": "^5.0.29", "buffer": "^6.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.542.0", "next": "15.5.2", "react": "19.1.0", "react-dom": "19.1.0", "remark-gfm": "^4.0.1", "ws": "^8.18.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "typescript": "^5"}}