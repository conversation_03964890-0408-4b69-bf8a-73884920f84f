"use client";

import { FC, useEffect, useState, useCallback } from 'react';
import { AlertTriangleIcon, ClockIcon, RefreshCwIcon, XIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { SessionMetrics, SESSION_LIMITS } from '@/lib/session-config';
import { cn } from '@/lib/utils';

interface SessionManagerProps {
  sessionMetrics: SessionMetrics;
  isConnected: boolean;
  onReconnect: () => Promise<void>;
  onDisconnect: () => void;
  className?: string;
}

interface SessionAlert {
  id: string;
  type: 'warning' | 'error' | 'info';
  title: string;
  message: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  dismissible?: boolean;
  autoHide?: number; // milliseconds
}

export const SessionManager: FC<SessionManagerProps> = ({
  sessionMetrics,
  isConnected,
  onReconnect,
  onDisconnect,
  className
}) => {
  const [alerts, setAlerts] = useState<SessionAlert[]>([]);
  const [isReconnecting, setIsReconnecting] = useState(false);

  const addAlert = useCallback((alert: Omit<SessionAlert, 'id'>) => {
    const id = `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const newAlert: SessionAlert = { ...alert, id };
    
    setAlerts(prev => [...prev, newAlert]);

    // Auto-hide if specified
    if (alert.autoHide) {
      setTimeout(() => {
        setAlerts(prev => prev.filter(a => a.id !== id));
      }, alert.autoHide);
    }
  }, []);

  const dismissAlert = useCallback((id: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== id));
  }, []);

  const handleReconnect = useCallback(async () => {
    setIsReconnecting(true);
    try {
      await onReconnect();
      addAlert({
        type: 'info',
        title: 'Reconnected',
        message: 'Successfully reconnected to the server with conversation history preserved.',
        autoHide: 5000
      });
    } catch {
      addAlert({
        type: 'error',
        title: 'Reconnection Failed',
        message: 'Failed to reconnect to the server. Please try again.',
        action: {
          label: 'Retry',
          onClick: handleReconnect
        }
      });
    } finally {
      setIsReconnecting(false);
    }
  }, [onReconnect, addAlert]);

  // Monitor session duration and show alerts
  useEffect(() => {
    if (!isConnected) return;

    const { duration } = sessionMetrics;
    const timeRemaining = SESSION_LIMITS.MAX_DURATION_MS - duration;
    const warningThreshold = SESSION_LIMITS.MAX_DURATION_MS - SESSION_LIMITS.WARNING_THRESHOLD_MS;
    const reconnectThreshold = SESSION_LIMITS.MAX_DURATION_MS - SESSION_LIMITS.RECONNECT_THRESHOLD_MS;

    // Show warning at 25 minutes (5 minutes remaining)
    if (duration >= SESSION_LIMITS.WARNING_THRESHOLD_MS && timeRemaining > warningThreshold) {
      const minutesRemaining = Math.ceil(timeRemaining / 60000);
      addAlert({
        type: 'warning',
        title: 'Session Expiring Soon',
        message: `Your session will expire in ${minutesRemaining} minute${minutesRemaining !== 1 ? 's' : ''}. Consider reconnecting to preserve your conversation.`,
        action: {
          label: 'Reconnect Now',
          onClick: handleReconnect
        },
        dismissible: true
      });
    }

    // Show reconnect prompt at 28 minutes (2 minutes remaining)
    if (duration >= SESSION_LIMITS.RECONNECT_THRESHOLD_MS && timeRemaining > reconnectThreshold) {
      addAlert({
        type: 'error',
        title: 'Session About to Expire',
        message: 'Your session will expire in less than 2 minutes. Reconnect now to avoid losing your conversation.',
        action: {
          label: 'Reconnect Now',
          onClick: handleReconnect
        }
      });
    }

    // Force disconnect at 30 minutes
    if (duration >= SESSION_LIMITS.MAX_DURATION_MS) {
      addAlert({
        type: 'error',
        title: 'Session Expired',
        message: 'Your session has expired. You have been disconnected.',
        action: {
          label: 'Start New Session',
          onClick: onDisconnect
        }
      });
      onDisconnect();
    }
  }, [sessionMetrics.duration, sessionMetrics, isConnected, addAlert, handleReconnect, onDisconnect]);

  // Monitor reconnection count
  useEffect(() => {
    if (sessionMetrics.reconnectionCount > 0) {
      addAlert({
        type: 'info',
        title: 'Session Reconnected',
        message: `Automatically reconnected ${sessionMetrics.reconnectionCount} time${sessionMetrics.reconnectionCount !== 1 ? 's' : ''} to maintain your session.`,
        autoHide: 3000,
        dismissible: true
      });
    }
  }, [sessionMetrics.reconnectionCount, addAlert]);

  const formatTimeRemaining = (duration: number): string => {
    const remaining = Math.max(0, SESSION_LIMITS.MAX_DURATION_MS - duration);
    const minutes = Math.floor(remaining / 60000);
    const seconds = Math.floor((remaining % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getProgressPercentage = (): number => {
    return Math.min(100, (sessionMetrics.duration / SESSION_LIMITS.MAX_DURATION_MS) * 100);
  };

  const getProgressColor = (): string => {
    const percentage = getProgressPercentage();
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-orange-500';
    if (percentage >= 50) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  if (!isConnected && alerts.length === 0) {
    return null;
  }

  return (
    <div className={cn("space-y-2", className)}>
      {/* Session Progress Bar */}
      {isConnected && (
        <div className="bg-white border border-gray-200 rounded-lg p-3">
          <div className="flex items-center justify-between mb-2">
            <div className="flex items-center gap-2">
              <ClockIcon className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Session Time</span>
            </div>
            <span className="text-sm font-mono text-gray-600">
              {formatTimeRemaining(sessionMetrics.duration)} remaining
            </span>
          </div>
          
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={cn("h-2 rounded-full transition-all duration-300", getProgressColor())}
              style={{ width: `${getProgressPercentage()}%` }}
            />
          </div>
          
          <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
            <span>Messages: {sessionMetrics.messagesExchanged}</span>
            <span>Reconnections: {sessionMetrics.reconnectionCount}</span>
          </div>
        </div>
      )}

      {/* Alerts */}
      {alerts.map(alert => (
        <div
          key={alert.id}
          className={cn(
            "border rounded-lg p-4",
            alert.type === 'error' && "bg-red-50 border-red-200",
            alert.type === 'warning' && "bg-orange-50 border-orange-200",
            alert.type === 'info' && "bg-blue-50 border-blue-200"
          )}
        >
          <div className="flex items-start justify-between">
            <div className="flex items-start gap-3">
              <AlertTriangleIcon 
                className={cn(
                  "w-5 h-5 mt-0.5",
                  alert.type === 'error' && "text-red-500",
                  alert.type === 'warning' && "text-orange-500",
                  alert.type === 'info' && "text-blue-500"
                )}
              />
              <div className="flex-1">
                <h4 className={cn(
                  "font-medium",
                  alert.type === 'error' && "text-red-800",
                  alert.type === 'warning' && "text-orange-800",
                  alert.type === 'info' && "text-blue-800"
                )}>
                  {alert.title}
                </h4>
                <p className={cn(
                  "text-sm mt-1",
                  alert.type === 'error' && "text-red-700",
                  alert.type === 'warning' && "text-orange-700",
                  alert.type === 'info' && "text-blue-700"
                )}>
                  {alert.message}
                </p>
                
                {alert.action && (
                  <div className="mt-3">
                    <Button
                      onClick={alert.action.onClick}
                      disabled={isReconnecting}
                      className={cn(
                        alert.type === 'error' && "bg-red-600 hover:bg-red-700",
                        alert.type === 'warning' && "bg-orange-600 hover:bg-orange-700",
                        alert.type === 'info' && "bg-blue-600 hover:bg-blue-700"
                      )}
                    >
                      {isReconnecting && alert.action.label.includes('Reconnect') ? (
                        <>
                          <RefreshCwIcon className="w-4 h-4 mr-2 animate-spin" />
                          Reconnecting...
                        </>
                      ) : (
                        alert.action.label
                      )}
                    </Button>
                  </div>
                )}
              </div>
            </div>
            
            {alert.dismissible && (
              <button
                onClick={() => dismissAlert(alert.id)}
                className={cn(
                  "p-1 rounded hover:bg-opacity-20",
                  alert.type === 'error' && "text-red-500 hover:bg-red-500",
                  alert.type === 'warning' && "text-orange-500 hover:bg-orange-500",
                  alert.type === 'info' && "text-blue-500 hover:bg-blue-500"
                )}
              >
                <XIcon className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      ))}
    </div>
  );
};
