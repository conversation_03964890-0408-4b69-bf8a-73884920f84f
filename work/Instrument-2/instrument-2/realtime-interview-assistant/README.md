# Realtime Interview Assistant

A web-based application for conducting mock system design interviews using OpenAI's Realtime API. This application provides low-latency speech-to-speech interactions with manual control over AI responses, perfect for practicing technical interviews.

## Features

- **Real-time Audio Processing**: Continuous microphone capture with server-side buffering
- **Manual Response Control**: Disable automatic voice activity detection for controlled AI responses
- **Audio Controls**: Adjustable playback speed (0.5x to 2x), mute/unmute functionality
- **Session Management**: 30-minute sessions with automatic reconnection and conversation history preservation
- **Interview-Specific AI**: Custom system prompt optimized for system design interview scenarios
- **Transcript Display**: Real-time text transcription alongside audio output
- **Keyboard Shortcuts**: Quick access to common functions

## System Architecture

### Core Components

1. **WebSocket Manager**: Handles connection to OpenAI Realtime API with authentication and reconnection logic
2. **Audio Input Pipeline**: Continuous microphone capture, PCM encoding, and server-side buffering
3. **Audio Output Pipeline**: Real-time audio playback with speed control and mute functionality
4. **Session Configuration**: Interview-specific prompts and 30-minute session management
5. **UI Components**: Built with assistant-ui for professional chat interface

### Key Design Decisions

- **Server-side VAD Disabled**: Uses `turn_detection: null` to prevent automatic responses
- **Manual Trigger System**: Spacebar or button press to commit audio buffer and request response
- **Echo Cancellation**: Browser-based echo cancellation for single microphone setup
- **Conversation History**: Maintains context across reconnections

## Prerequisites

- Node.js 18+ and npm
- OpenAI API key with Realtime API access
- Modern web browser with WebRTC support
- Microphone and headphones/earphones (recommended to minimize echo)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd realtime-interview-assistant
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.local.example .env.local
```

4. Add your OpenAI API key to `.env.local`:
```
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
```

## Usage

1. Start the development server:
```bash
npm run dev
```

2. Open your browser and navigate to `http://localhost:3000`

3. The app will automatically connect to OpenAI Realtime API

4. Click "Start session" to begin recording

5. Speak your interview questions or responses

6. Press "Generate" (or Spacebar) to get AI feedback

### Keyboard Shortcuts

- **Spacebar**: Generate AI response
- **M**: Toggle mute/unmute
- **R**: Toggle recording

## Configuration

### Voice Options

Choose from available OpenAI voices:
- Alloy (Neutral)
- Echo (Male)
- Fable (British Male)
- Onyx (Deep Male)
- Nova (Female)
- Shimmer (Soft Female)

### Interview Types

- **System Design** (default): Focuses on architecture, scalability, and distributed systems
- **Coding**: Algorithm and data structure interviews
- **Behavioral**: Leadership and communication skills

### Session Limits

- Maximum session duration: 30 minutes
- Warning at 25 minutes
- Auto-reconnect at 28 minutes
- Maximum conversation items: 100

## API Integration

### OpenAI Realtime API

The application connects to:
```
wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01
```

Key API features used:
- Server-side input audio buffering
- Manual turn detection control
- Real-time audio streaming
- Conversation history management

### Audio Processing

- **Input**: PCM 16-bit, 24kHz, mono
- **Output**: Real-time streaming with speed control
- **Buffering**: Server-side for efficient processing
- **Encoding**: Base64 for WebSocket transmission

## Development

### Project Structure

```
src/
├── components/
│   ├── assistant-ui/          # Chat interface components
│   ├── ui/                    # Basic UI components
│   └── realtime-interview-app.tsx
├── hooks/
│   ├── useRealtimeWebSocket.ts
│   ├── useAudioInput.ts
│   ├── useAudioOutput.ts
│   └── useRealtimeInterview.ts
├── lib/
│   ├── websocket-manager.ts
│   ├── audio-input.ts
│   ├── audio-output.ts
│   └── session-config.ts
└── types/
    └── realtime.ts
```

### Building

```bash
npm run build
```

### Linting

```bash
npm run lint
```

## Troubleshooting

### Common Issues

1. **Microphone Access Denied**
   - Ensure browser permissions are granted
   - Use HTTPS in production

2. **Echo/Feedback**
   - Use headphones or earphones
   - Check browser echo cancellation settings

3. **Connection Issues**
   - Verify API key is correct
   - Check network connectivity
   - Ensure Realtime API access

4. **Audio Quality Issues**
   - Check microphone quality
   - Ensure stable internet connection
   - Try different audio devices

### Performance Optimization

- Use Chrome or Firefox for best WebRTC support
- Close unnecessary browser tabs
- Ensure stable internet connection (>1 Mbps recommended)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Acknowledgments

- OpenAI for the Realtime API
- assistant-ui for the chat interface components
- The open-source community for various audio processing libraries
