"use client";

import { RealtimeInterviewApp } from '@/components/realtime-interview-app';

export default function Home() {
  // Get API key from environment variable
  const apiKey = process.env.NEXT_PUBLIC_OPENAI_API_KEY || '';

  if (!apiKey) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md mx-auto text-center p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            API Key Required
          </h1>
          <p className="text-gray-600 mb-4">
            Please add your OpenAI API key to the environment variables to use the Realtime Interview Assistant.
          </p>
          <div className="bg-gray-100 p-4 rounded-lg text-left">
            <p className="text-sm font-mono text-gray-800">
              NEXT_PUBLIC_OPENAI_API_KEY=your_api_key_here
            </p>
          </div>
          <p className="text-sm text-gray-500 mt-4">
            Add this to your .env.local file and restart the development server.
          </p>
        </div>
      </div>
    );
  }

  return <RealtimeInterviewApp apiKey={apiKey} />;
}
