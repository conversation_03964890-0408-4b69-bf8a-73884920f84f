import { openai } from "@ai-sdk/openai";
import { convertToModelMessages, streamText } from "ai";

export const maxDuration = 30;

export async function POST(req: Request) {
  const { messages } = await req.json();
  
  const result = streamText({
    model: openai("gpt-4o-mini"),
    messages: convertToModelMessages(messages),
    system: `You are an AI interview assistant specializing in system design interviews. 
    
Your role is to:
1. Ask thoughtful system design questions
2. Provide constructive feedback on solutions
3. Guide candidates through the design process
4. Help identify areas for improvement
5. Simulate a realistic interview experience

Keep your responses conversational and encouraging while maintaining professional standards.`,
  });
  
  return result.toUIMessageStreamResponse();
}
