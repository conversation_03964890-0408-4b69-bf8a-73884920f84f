"use client";

import { FC } from 'react';
import { 
  WifiIcon, 
  WifiOffIcon, 
  ClockIcon, 
  AlertTriangleIcon, 
  CheckCircleIcon,
  XCircleIcon,
  RefreshCwIcon,
  ActivityIcon
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ConnectionState } from '@/types/realtime';
import { SessionMetrics } from '@/lib/session-config';

interface SessionStatusProps {
  connectionState: ConnectionState;
  isConnected: boolean;
  sessionMetrics: SessionMetrics;
  isSessionExpired: boolean;
  shouldShowWarning: boolean;
  error?: string | null;
  className?: string;
}

export const SessionStatus: FC<SessionStatusProps> = ({
  connectionState,
  isConnected,
  sessionMetrics,
  isSessionExpired,
  shouldShowWarning,
  error,
  className
}) => {
  const formatDuration = (ms: number): string => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getConnectionIcon = () => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return <WifiIcon className="w-5 h-5 text-green-500" />;
      case ConnectionState.CONNECTING:
        return <RefreshCwIcon className="w-5 h-5 text-yellow-500 animate-spin" />;
      case ConnectionState.RECONNECTING:
        return <RefreshCwIcon className="w-5 h-5 text-orange-500 animate-spin" />;
      case ConnectionState.ERROR:
        return <WifiOffIcon className="w-5 h-5 text-red-500" />;
      default:
        return <WifiOffIcon className="w-5 h-5 text-gray-400" />;
    }
  };

  const getConnectionText = () => {
    switch (connectionState) {
      case ConnectionState.CONNECTED:
        return 'Connected';
      case ConnectionState.CONNECTING:
        return 'Connecting...';
      case ConnectionState.RECONNECTING:
        return 'Reconnecting...';
      case ConnectionState.ERROR:
        return 'Connection Error';
      default:
        return 'Disconnected';
    }
  };

  const getStatusColor = () => {
    if (error) return 'border-red-200 bg-red-50';
    if (isSessionExpired) return 'border-red-200 bg-red-50';
    if (shouldShowWarning) return 'border-orange-200 bg-orange-50';
    if (isConnected) return 'border-green-200 bg-green-50';
    if (connectionState === ConnectionState.CONNECTING) return 'border-yellow-200 bg-yellow-50';
    return 'border-slate-200 bg-slate-50';
  };

  return (
    <div className={cn(
      "rounded-xl border p-4 transition-all duration-300",
      getStatusColor(),
      className
    )}>
      <div className="flex items-center justify-between">
        {/* Connection Status */}
        <div className="flex items-center gap-3">
          {getConnectionIcon()}
          <div>
            <div className="font-semibold text-sm">
              {getConnectionText()}
            </div>
            <div className="text-xs text-slate-500">
              OpenAI Realtime API
            </div>
          </div>
        </div>

        {/* Session Timer */}
        {isConnected && (
          <div className="flex items-center gap-2">
            <ClockIcon className="w-4 h-4 text-slate-500" />
            <span className={cn(
              "font-mono text-sm font-semibold",
              shouldShowWarning ? "text-orange-600" : 
              isSessionExpired ? "text-red-600" : "text-slate-700"
            )}>
              {formatDuration(sessionMetrics.duration)}
            </span>
          </div>
        )}
      </div>

      {/* Status Messages */}
      {error && (
        <div className="mt-3 flex items-center gap-2 text-red-700">
          <XCircleIcon className="w-4 h-4" />
          <span className="text-sm font-medium">{error}</span>
        </div>
      )}

      {isSessionExpired && (
        <div className="mt-3 flex items-center gap-2 text-red-700">
          <AlertTriangleIcon className="w-4 h-4" />
          <span className="text-sm font-medium">Session has expired</span>
        </div>
      )}

      {shouldShowWarning && !isSessionExpired && (
        <div className="mt-3 flex items-center gap-2 text-orange-700">
          <AlertTriangleIcon className="w-4 h-4" />
          <span className="text-sm font-medium">Session expires in 5 minutes</span>
        </div>
      )}

      {isConnected && !error && !shouldShowWarning && (
        <div className="mt-3 flex items-center gap-2 text-green-700">
          <CheckCircleIcon className="w-4 h-4" />
          <span className="text-sm font-medium">Ready for interview</span>
        </div>
      )}

      {/* Session Statistics */}
      {isConnected && (
        <div className="mt-4 pt-3 border-t border-slate-200/60">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-slate-900">
                {sessionMetrics.messagesExchanged}
              </div>
              <div className="text-xs text-slate-500">Messages</div>
            </div>
            <div>
              <div className="text-lg font-bold text-slate-900">
                {sessionMetrics.audioChunksProcessed}
              </div>
              <div className="text-xs text-slate-500">Audio Chunks</div>
            </div>
            <div>
              <div className="text-lg font-bold text-slate-900">
                {sessionMetrics.reconnectionCount}
              </div>
              <div className="text-xs text-slate-500">Reconnects</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Compact version for smaller spaces
export const SessionStatusCompact: FC<SessionStatusProps> = ({
  connectionState,
  isConnected,
  sessionMetrics,
  isSessionExpired,
  shouldShowWarning,
  error,
  className
}) => {
  const formatDuration = (ms: number): string => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getStatusIcon = () => {
    if (error || isSessionExpired) return <XCircleIcon className="w-4 h-4 text-red-500" />;
    if (shouldShowWarning) return <AlertTriangleIcon className="w-4 h-4 text-orange-500" />;
    if (isConnected) return <CheckCircleIcon className="w-4 h-4 text-green-500" />;
    if (connectionState === ConnectionState.CONNECTING) return <RefreshCwIcon className="w-4 h-4 text-yellow-500 animate-spin" />;
    return <WifiOffIcon className="w-4 h-4 text-gray-400" />;
  };

  const getStatusText = () => {
    if (error) return 'Error';
    if (isSessionExpired) return 'Expired';
    if (shouldShowWarning) return 'Expiring Soon';
    if (isConnected) return 'Connected';
    if (connectionState === ConnectionState.CONNECTING) return 'Connecting';
    return 'Disconnected';
  };

  return (
    <div className={cn(
      "flex items-center gap-3 px-3 py-2 rounded-lg border bg-white shadow-sm",
      className
    )}>
      {getStatusIcon()}
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">{getStatusText()}</span>
        {isConnected && (
          <>
            <span className="text-slate-300">•</span>
            <span className="text-sm font-mono text-slate-600">
              {formatDuration(sessionMetrics.duration)}
            </span>
          </>
        )}
      </div>
    </div>
  );
};

// Activity indicator for showing real-time activity
export const ActivityIndicator: FC<{
  isActive: boolean;
  label: string;
  color?: 'blue' | 'green' | 'red' | 'orange';
  className?: string;
}> = ({ isActive, label, color = 'blue', className }) => {
  const colorClasses = {
    blue: 'text-blue-600 bg-blue-50 border-blue-200',
    green: 'text-green-600 bg-green-50 border-green-200',
    red: 'text-red-600 bg-red-50 border-red-200',
    orange: 'text-orange-600 bg-orange-50 border-orange-200'
  };

  return (
    <div className={cn(
      "flex items-center gap-2 px-3 py-2 rounded-lg border transition-all duration-200",
      isActive ? colorClasses[color] : "text-slate-500 bg-slate-50 border-slate-200",
      className
    )}>
      <div className={cn(
        "w-2 h-2 rounded-full transition-all duration-200",
        isActive ? `bg-${color}-500 animate-pulse` : "bg-slate-300"
      )} />
      <span className="text-sm font-medium">{label}</span>
      {isActive && <ActivityIcon className="w-3 h-3" />}
    </div>
  );
};
