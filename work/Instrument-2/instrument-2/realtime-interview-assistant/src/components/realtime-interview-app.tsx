"use client";

import { FC, useEffect, useCallback, useState } from 'react';
import { AssistantRuntimeProvider } from "@assistant-ui/react";
import { useChatRuntime } from "@assistant-ui/react-ai-sdk";
import { TooltipProvider } from "@/components/ui/tooltip";
import { RealtimeThread } from '@/components/assistant-ui/realtime-thread';
import { ControlPanel } from '@/components/assistant-ui/control-panel';
import { useRealtimeInterview } from '@/hooks/useRealtimeInterview';
import { VoiceOption } from '@/lib/session-config';
import { WifiIcon, WifiOffIcon, AlertTriangleIcon, CheckCircleIcon, ClockIcon } from 'lucide-react';

interface RealtimeInterviewAppProps {
  apiKey: string;
}

export const RealtimeInterviewApp: FC<RealtimeInterviewAppProps> = ({ apiKey }) => {
  // Set up the chat runtime for assistant-ui
  const runtime = useChatRuntime();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);

  const {
    connectionState,
    isConnected,
    isRecording,
    isPlaying,
    isMuted,
    playbackRate,
    sessionMetrics,
    isSessionExpired,
    shouldShowWarning,
    connect,
    disconnect,
    startRecording,
    stopRecording,
    triggerResponse,
    setMuted,
    setPlaybackRate,
    error
  } = useRealtimeInterview({
    apiKey,
    sessionConfig: {
      voice: 'alloy',
      enableTranscription: true,
      interviewType: 'system-design'
    },
    autoConnect: true,
    onError: (error) => {
      console.error('Interview session error:', error);
      setShowSuccessMessage(false);
    },
    onSessionExpired: () => {
      console.log('Session expired, disconnecting...');
      setShowSuccessMessage(false);
    },
    onWarning: (message) => {
      console.warn('Session warning:', message);
    }
  });

  // Show success message when connected
  useEffect(() => {
    if (isConnected) {
      setShowSuccessMessage(true);
      const timer = setTimeout(() => setShowSuccessMessage(false), 3000);
      return () => clearTimeout(timer);
    }
  }, [isConnected]);

  // Simulate audio levels for demo purposes
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isRecording || isPlaying) {
      interval = setInterval(() => {
        // Simulate realistic audio levels with some variation
        const baseLevel = isRecording ? 0.6 : 0.4;
        const variation = Math.random() * 0.4;
        const newLevel = Math.min(1, Math.max(0, baseLevel + variation - 0.2));
        setAudioLevel(newLevel);
      }, 100);
    } else {
      setAudioLevel(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording, isPlaying]);

  // Enhanced keyboard shortcuts with accessibility
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ignore if user is typing in an input field or has modifier keys pressed
      if (event.target instanceof HTMLInputElement ||
          event.target instanceof HTMLTextAreaElement ||
          event.ctrlKey || event.metaKey || event.altKey) {
        return;
      }

      // Handle keyboard shortcuts
      switch (event.key.toLowerCase()) {
        case ' ':
          event.preventDefault();
          if (isConnected) {
            triggerResponse();
            // Announce action for screen readers
            announceAction('AI response triggered');
          } else {
            announceAction('Please connect first');
          }
          break;
        case 'm':
          event.preventDefault();
          if (isConnected) {
            setMuted(!isMuted);
            announceAction(isMuted ? 'Audio unmuted' : 'Audio muted');
          } else {
            announceAction('Please connect first');
          }
          break;
        case 'r':
          event.preventDefault();
          if (isConnected) {
            if (isRecording) {
              stopRecording();
              announceAction('Recording stopped');
            } else {
              startRecording();
              announceAction('Recording started');
            }
          } else {
            announceAction('Please wait for connection');
          }
          break;
        case '?':
          event.preventDefault();
          showKeyboardShortcuts();
          break;
        case '1':
        case '2':
        case '3':
        case '4':
          event.preventDefault();
          const rates = [0.75, 1.0, 1.25, 1.5];
          const rateIndex = parseInt(event.key) - 1;
          if (isConnected && rates[rateIndex]) {
            setPlaybackRate(rates[rateIndex]);
            announceAction(`Playback speed set to ${rates[rateIndex]}x`);
          }
          break;
      }
    };

    // Announce keyboard shortcuts on focus
    const handleFocus = () => {
      if (document.activeElement === document.body) {
        announceAction('Interview assistant focused. Press ? for keyboard shortcuts');
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    window.addEventListener('focus', handleFocus);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('focus', handleFocus);
    };
  }, [isConnected, isRecording, isMuted, triggerResponse, setMuted, startRecording, stopRecording, disconnect, connect, setPlaybackRate]);

  // Screen reader announcements
  const announceAction = (message: string) => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.setAttribute('aria-atomic', 'true');
    announcement.className = 'sr-only';
    announcement.textContent = message;
    document.body.appendChild(announcement);
    setTimeout(() => document.body.removeChild(announcement), 1000);
  };

  // Show keyboard shortcuts modal
  const showKeyboardShortcuts = () => {
    const shortcuts = [
      'Space: Generate AI Response',
      'R: Toggle Recording',
      'M: Toggle Mute',
      '1-4: Set Playback Speed',
      '?: Show this help'
    ];
    alert('Keyboard Shortcuts:\n\n' + shortcuts.join('\n'));
  };

  const handleToggleRecording = useCallback(() => {
    if (isRecording) {
      stopRecording();
    } else {
      startRecording();
    }
  }, [isRecording, startRecording, stopRecording]);

  const handleVoiceChange = useCallback((voice: VoiceOption) => {
    // Voice change would require reconnection with new session config
    // For now, we'll just log it - in a full implementation, you'd want to
    // disconnect, update the session config, and reconnect
    console.log('Voice change requested:', voice);
  }, []);

  // Note: In a complete implementation, you'd integrate the WebSocket events with assistant-ui
  // by creating a proper AssistantRuntime that connects to your realtime WebSocket events

  const formatDuration = (ms: number): string => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const getConnectionStatusIcon = () => {
    if (isConnected) return <WifiIcon className="w-5 h-5 text-green-500" />;
    if (connectionState === 'connecting') return <WifiIcon className="w-5 h-5 text-yellow-500 animate-pulse" />;
    if (connectionState === 'error') return <WifiOffIcon className="w-5 h-5 text-red-500" />;
    return <WifiOffIcon className="w-5 h-5 text-gray-400" />;
  };

  const getConnectionStatusText = () => {
    if (isConnected) return 'Connected';
    if (connectionState === 'connecting') return 'Connecting...';
    if (connectionState === 'error') return 'Connection Error';
    return 'Disconnected';
  };

  return (
    <AssistantRuntimeProvider runtime={runtime}>
      <TooltipProvider>
        <div className="h-screen flex flex-col bg-gradient-to-br from-slate-50 to-blue-50">
          {/* Success Message */}
          {showSuccessMessage && (
            <div className="fixed top-4 right-4 z-50 animate-fade-in">
              <div className="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg flex items-center gap-2">
                <CheckCircleIcon className="w-5 h-5" />
                <span className="font-medium">Successfully connected to OpenAI Realtime API!</span>
              </div>
            </div>
          )}

          {/* Header */}
          <header className="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 px-6 py-4 shadow-sm">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">AI</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-900 to-slate-700 bg-clip-text text-transparent">
                    Realtime Interview Assistant
                  </h1>
                  <p className="text-sm text-slate-600 font-medium">
                    Practice system design interviews with AI feedback
                  </p>
                </div>
              </div>

              {/* Status indicators */}
              <div className="flex items-center gap-4">
                {/* Connection Status */}
                <div className="flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-sm border border-slate-200">
                  {getConnectionStatusIcon()}
                  <span className="text-sm font-medium text-slate-700">
                    {getConnectionStatusText()}
                  </span>
                </div>

                {/* Session Timer */}
                {isConnected && (
                  <div className="flex items-center gap-2 px-3 py-2 bg-white rounded-lg shadow-sm border border-slate-200">
                    <ClockIcon className="w-4 h-4 text-slate-500" />
                    <span className={`text-sm font-mono font-medium ${
                      shouldShowWarning ? 'text-orange-600' :
                      isSessionExpired ? 'text-red-600' : 'text-slate-700'
                    }`}>
                      {formatDuration(sessionMetrics.duration)}
                    </span>
                  </div>
                )}

                {/* Warning indicators */}
                {shouldShowWarning && (
                  <div className="flex items-center gap-2 px-3 py-2 bg-orange-50 text-orange-700 rounded-lg border border-orange-200 animate-pulse">
                    <AlertTriangleIcon className="w-4 h-4" />
                    <span className="text-sm font-medium">Session expires soon</span>
                  </div>
                )}

                {isSessionExpired && (
                  <div className="flex items-center gap-2 px-3 py-2 bg-red-50 text-red-700 rounded-lg border border-red-200">
                    <AlertTriangleIcon className="w-4 h-4" />
                    <span className="text-sm font-medium">Session expired</span>
                  </div>
                )}

                {error && (
                  <div className="flex items-center gap-2 px-3 py-2 bg-red-50 text-red-700 rounded-lg border border-red-200 max-w-xs">
                    <AlertTriangleIcon className="w-4 h-4 flex-shrink-0" />
                    <span className="text-sm font-medium truncate">{error}</span>
                  </div>
                )}
              </div>
            </div>
          </header>

          {/* Main Content */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Chat Interface */}
            <div className="flex-1 overflow-hidden relative">
              <div className="h-full p-6">
                <div className="max-w-4xl mx-auto h-full">
                  <RealtimeThread
                    isRecording={isRecording}
                    isMuted={isMuted}
                    isPlaying={isPlaying}
                    playbackRate={playbackRate}
                    audioLevel={audioLevel}
                    onToggleRecording={handleToggleRecording}
                    onToggleMute={() => setMuted(!isMuted)}
                    onTriggerResponse={triggerResponse}
                    onPlaybackRateChange={setPlaybackRate}
                    connectionStatus={
                      isConnected ? 'connected' :
                      connectionState === 'connecting' ? 'connecting' :
                      connectionState === 'error' ? 'error' : 'disconnected'
                    }
                  />
                </div>
              </div>

              {/* Gradient overlay for better visual separation */}
              <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-slate-50 to-transparent pointer-events-none" />
            </div>

            {/* Control Panel */}
            <div className="border-t border-slate-200/60 bg-white/80 backdrop-blur-sm">
              <div className="max-w-4xl mx-auto">
                <ControlPanel
                  connectionState={connectionState}
                  isConnected={isConnected}
                  isRecording={isRecording}
                  isPlaying={isPlaying}
                  isMuted={isMuted}
                  playbackRate={playbackRate}
                  sessionMetrics={sessionMetrics}
                  isSessionExpired={isSessionExpired}
                  shouldShowWarning={shouldShowWarning}
                  onToggleRecording={handleToggleRecording}
                  onTriggerResponse={triggerResponse}
                  onToggleMute={() => setMuted(!isMuted)}
                  onPlaybackRateChange={setPlaybackRate}
                  onVoiceChange={handleVoiceChange}
                  error={error}
                />
              </div>
            </div>
          </div>

          {/* Instructions Footer */}
          <footer className="bg-slate-900 text-slate-300 px-6 py-4 border-t border-slate-700">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <kbd className="bg-slate-800 text-slate-200 px-2 py-1 rounded border border-slate-600 font-mono text-xs">Space</kbd>
                    <span>Trigger AI Response</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <kbd className="bg-slate-800 text-slate-200 px-2 py-1 rounded border border-slate-600 font-mono text-xs">M</kbd>
                    <span>Toggle Mute</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <kbd className="bg-slate-800 text-slate-200 px-2 py-1 rounded border border-slate-600 font-mono text-xs">R</kbd>
                    <span>Toggle Recording</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <kbd className="bg-slate-800 text-slate-200 px-2 py-1 rounded border border-slate-600 font-mono text-xs">Esc</kbd>
                    <span>Disconnect</span>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-slate-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="font-medium">OpenAI Realtime API</span>
                  <span>•</span>
                  <span>System Design Interview Practice</span>
                </div>
              </div>
            </div>
          </footer>
        </div>
      </TooltipProvider>
    </AssistantRuntimeProvider>
  );
};
