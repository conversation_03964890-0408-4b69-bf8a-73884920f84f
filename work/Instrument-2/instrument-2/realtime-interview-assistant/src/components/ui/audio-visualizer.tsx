"use client";

import { FC, useEffect, useRef, useState, useMemo } from 'react';
import { cn } from '@/lib/utils';

interface AudioVisualizerProps {
  isRecording?: boolean;
  isPlaying?: boolean;
  audioLevel?: number;
  className?: string;
  variant?: 'waveform' | 'bars' | 'circle';
  size?: 'sm' | 'md' | 'lg';
  color?: 'blue' | 'green' | 'red' | 'purple';
}

export const AudioVisualizer: FC<AudioVisualizerProps> = ({
  isRecording = false,
  isPlaying = false,
  audioLevel = 0,
  className,
  variant = 'waveform',
  size = 'md',
  color = 'blue'
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number | undefined>(undefined);
  const [bars, setBars] = useState<number[]>(Array(32).fill(0));

  const sizeClasses = {
    sm: 'w-32 h-8',
    md: 'w-48 h-12',
    lg: 'w-64 h-16'
  };

  const colorSchemes = useMemo(() => ({
    blue: {
      primary: '#3b82f6',
      secondary: '#60a5fa',
      gradient: ['#3b82f6', '#1d4ed8']
    },
    green: {
      primary: '#10b981',
      secondary: '#34d399',
      gradient: ['#10b981', '#047857']
    },
    red: {
      primary: '#ef4444',
      secondary: '#f87171',
      gradient: ['#ef4444', '#dc2626']
    },
    purple: {
      primary: '#8b5cf6',
      secondary: '#a78bfa',
      gradient: ['#8b5cf6', '#7c3aed']
    }
  }), []);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const drawWaveform = (ctx: CanvasRenderingContext2D, width: number, height: number, isStatic = false) => {
      const centerY = height / 2;
      const amplitude = isStatic ? height * 0.1 : height * 0.4 * (audioLevel + 0.1);
      const frequency = 0.02;
      const time = isStatic ? 0 : Date.now() * 0.005;

      // Create gradient
      const gradient = ctx.createLinearGradient(0, 0, width, 0);
      gradient.addColorStop(0, colorSchemes[color].gradient[0]);
      gradient.addColorStop(1, colorSchemes[color].gradient[1]);

      ctx.strokeStyle = gradient;
      ctx.lineWidth = 3;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      ctx.beginPath();
      for (let x = 0; x < width; x++) {
        const y = centerY + Math.sin(x * frequency + time) * amplitude *
                  (isRecording ? (1 + Math.sin(time * 2) * 0.3) : 1);
        if (x === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.stroke();

      // Add glow effect
      if (isRecording || isPlaying) {
        ctx.shadowColor = colorSchemes[color].primary;
        ctx.shadowBlur = 10;
        ctx.stroke();
        ctx.shadowBlur = 0;
      }
    };

    const drawBars = (ctx: CanvasRenderingContext2D, width: number, height: number, isStatic = false) => {
      const barCount = 32;
      const barWidth = width / barCount;
      const maxBarHeight = height * 0.8;

      // Update bars with animation
      if (!isStatic && (isRecording || isPlaying)) {
        setBars(prev => prev.map((_, i) => {
          const baseHeight = audioLevel * maxBarHeight;
          const variation = Math.sin(Date.now() * 0.01 + i * 0.5) * 0.3 + 0.7;
          return Math.max(0.1, baseHeight * variation);
        }));
      }

      // Create gradient
      const gradient = ctx.createLinearGradient(0, height, 0, 0);
      gradient.addColorStop(0, colorSchemes[color].gradient[0]);
      gradient.addColorStop(1, colorSchemes[color].gradient[1]);

      ctx.fillStyle = gradient;

      bars.forEach((barHeight, i) => {
        const x = i * barWidth;
        const actualHeight = isStatic ? maxBarHeight * 0.2 : barHeight;
        const y = height - actualHeight;

        ctx.fillRect(x + 1, y, barWidth - 2, actualHeight);

        // Add glow effect for active bars
        if ((isRecording || isPlaying) && !isStatic) {
          ctx.shadowColor = colorSchemes[color].primary;
          ctx.shadowBlur = 5;
          ctx.fillRect(x + 1, y, barWidth - 2, actualHeight);
          ctx.shadowBlur = 0;
        }
      });
    };

    const drawCircle = (ctx: CanvasRenderingContext2D, width: number, height: number, isStatic = false) => {
      const centerX = width / 2;
      const centerY = height / 2;
      const baseRadius = Math.min(width, height) * 0.3;
      const radius = isStatic ? baseRadius : baseRadius + (audioLevel * baseRadius * 0.5);

      // Create radial gradient
      const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
      gradient.addColorStop(0, colorSchemes[color].gradient[0] + '80');
      gradient.addColorStop(1, colorSchemes[color].gradient[1] + '20');

      // Draw pulsing circle
      ctx.fillStyle = gradient;
      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.fill();

      // Draw border
      ctx.strokeStyle = colorSchemes[color].primary;
      ctx.lineWidth = 2;
      ctx.stroke();

      // Add glow effect
      if (isRecording || isPlaying) {
        ctx.shadowColor = colorSchemes[color].primary;
        ctx.shadowBlur = 15;
        ctx.stroke();
        ctx.shadowBlur = 0;
      }
    };

    const animate = () => {
      const { width, height } = canvas;
      ctx.clearRect(0, 0, width, height);

      if (variant === 'waveform') {
        drawWaveform(ctx, width, height);
      } else if (variant === 'bars') {
        drawBars(ctx, width, height);
      } else if (variant === 'circle') {
        drawCircle(ctx, width, height);
      }

      if (isRecording || isPlaying) {
        animationRef.current = requestAnimationFrame(animate);
      }
    };

    if (isRecording || isPlaying) {
      animate();
    } else {
      // Draw static state
      const { width, height } = canvas;
      ctx.clearRect(0, 0, width, height);
      if (variant === 'waveform') {
        drawWaveform(ctx, width, height, true);
      } else if (variant === 'bars') {
        drawBars(ctx, width, height, true);
      } else if (variant === 'circle') {
        drawCircle(ctx, width, height, true);
      }
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isRecording, isPlaying, audioLevel, variant, color, bars, colorSchemes]);



  return (
    <div className={cn(
      "relative overflow-hidden rounded-lg bg-slate-50 border border-slate-200",
      sizeClasses[size],
      className
    )}>
      <canvas
        ref={canvasRef}
        width={size === 'sm' ? 128 : size === 'md' ? 192 : 256}
        height={size === 'sm' ? 32 : size === 'md' ? 48 : 64}
        className="w-full h-full"
      />
      
      {/* Status indicator */}
      {(isRecording || isPlaying) && (
        <div className="absolute top-1 right-1">
          <div className={cn(
            "w-2 h-2 rounded-full animate-pulse",
            isRecording ? "bg-red-500" : "bg-green-500"
          )} />
        </div>
      )}
    </div>
  );
};

// Recording indicator component
export const RecordingIndicator: FC<{
  isRecording: boolean;
  className?: string;
}> = ({ isRecording, className }) => {
  return (
    <div className={cn(
      "flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200",
      isRecording 
        ? "bg-red-50 border border-red-200 text-red-700" 
        : "bg-slate-50 border border-slate-200 text-slate-500",
      className
    )}>
      <div className={cn(
        "w-3 h-3 rounded-full transition-all duration-200",
        isRecording ? "bg-red-500 animate-pulse" : "bg-slate-300"
      )} />
      <span className="text-sm font-medium">
        {isRecording ? "Recording..." : "Ready"}
      </span>
    </div>
  );
};

// Audio level meter component
export const AudioLevelMeter: FC<{
  level: number;
  className?: string;
}> = ({ level, className }) => {
  const percentage = Math.min(100, Math.max(0, level * 100));
  
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <span className="text-xs text-slate-500 font-medium">Level:</span>
      <div className="w-24 h-2 bg-slate-200 rounded-full overflow-hidden">
        <div 
          className={cn(
            "h-full transition-all duration-100 rounded-full",
            percentage > 80 ? "bg-red-500" :
            percentage > 60 ? "bg-yellow-500" :
            "bg-green-500"
          )}
          style={{ width: `${percentage}%` }}
        />
      </div>
      <span className="text-xs text-slate-500 font-mono min-w-[3ch]">
        {Math.round(percentage)}%
      </span>
    </div>
  );
};
