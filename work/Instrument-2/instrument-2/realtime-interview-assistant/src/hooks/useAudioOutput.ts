'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { AudioOutputManager } from '@/lib/audio-output';
import { AudioConfig } from '@/types/realtime';

interface UseAudioOutputOptions {
  autoInitialize?: boolean;
  initialPlaybackRate?: number;
  initialMuted?: boolean;
  onError?: (error: Error) => void;
}

interface UseAudioOutputReturn {
  isInitialized: boolean;
  isPlaying: boolean;
  isMuted: boolean;
  playbackRate: number;
  queueLength: number;
  outputDevices: MediaDeviceInfo[];
  selectedOutputDeviceId: string | null;
  audioConfig: AudioConfig | null;
  initialize: () => Promise<void>;
  addAudioChunk: (base64Audio: string, chunkId?: string) => void;
  setPlaybackRate: (rate: number) => void;
  setMuted: (muted: boolean) => void;
  setVolume: (volume: number) => void;
  stopPlayback: () => void;
  clearQueue: () => void;
  setOutputDevice: (deviceId: string) => Promise<void>;
  cleanup: () => void;
  error: string | null;
}

export function useAudioOutput({
  autoInitialize = false,
  initialPlaybackRate = 1.0,
  initialMuted = false,
  onError
}: UseAudioOutputOptions = {}): UseAudioOutputReturn {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMutedState] = useState(initialMuted);
  const [playbackRate, setPlaybackRateState] = useState(initialPlaybackRate);
  const [queueLength, setQueueLength] = useState(0);
  const [outputDevices, setOutputDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedOutputDeviceId, setSelectedOutputDeviceId] = useState<string | null>(null);
  const [audioConfig, setAudioConfig] = useState<AudioConfig | null>(null);
  const [error, setError] = useState<string | null>(null);

  const managerRef = useRef<AudioOutputManager | null>(null);
  const statusIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize manager
  useEffect(() => {
    managerRef.current = new AudioOutputManager();

    return () => {
      if (managerRef.current) {
        managerRef.current.cleanup();
      }
    };
  }, []);

  const initialize = useCallback(async () => {
    if (!managerRef.current) return;

    try {
      setError(null);
      await managerRef.current.initialize();
      
      setIsInitialized(true);
      setAudioConfig(managerRef.current.getAudioConfig());

      // Load available output devices
      const devices = await managerRef.current.getOutputDevices();
      setOutputDevices(devices);
      
      // Set default device (first available)
      if (devices.length > 0) {
        setSelectedOutputDeviceId(devices[0].deviceId);
      }

      console.log('Audio output initialized successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize audio output';
      setError(errorMessage);
      setIsInitialized(false);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [onError]);

  // Auto-initialize if enabled
  useEffect(() => {
    if (autoInitialize && !isInitialized) {
      initialize();
    }
  }, [autoInitialize, isInitialized, initialize]);

  // Apply initial settings after initialization
  useEffect(() => {
    if (isInitialized && managerRef.current) {
      managerRef.current.setPlaybackRate(initialPlaybackRate);
      managerRef.current.setMuted(initialMuted);
    }
  }, [isInitialized, initialPlaybackRate, initialMuted]);

  // Monitor playback status
  useEffect(() => {
    if (isInitialized && managerRef.current) {
      statusIntervalRef.current = setInterval(() => {
        if (managerRef.current) {
          setIsPlaying(managerRef.current.isActivelyPlaying());
          setQueueLength(managerRef.current.getQueueLength());
        }
      }, 100); // Update every 100ms

      return () => {
        if (statusIntervalRef.current) {
          clearInterval(statusIntervalRef.current);
        }
      };
    }
  }, [isInitialized]);

  const addAudioChunk = useCallback((base64Audio: string, chunkId?: string) => {
    if (!managerRef.current || !isInitialized) {
      setError('Audio output not initialized');
      return;
    }

    try {
      setError(null);
      managerRef.current.addAudioChunk(base64Audio, chunkId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add audio chunk';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, onError]);

  const setPlaybackRate = useCallback((rate: number) => {
    if (!managerRef.current || !isInitialized) {
      setError('Audio output not initialized');
      return;
    }

    try {
      setError(null);
      managerRef.current.setPlaybackRate(rate);
      setPlaybackRateState(rate);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to set playback rate';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, onError]);

  const setMuted = useCallback((muted: boolean) => {
    if (!managerRef.current || !isInitialized) {
      setError('Audio output not initialized');
      return;
    }

    try {
      setError(null);
      managerRef.current.setMuted(muted);
      setIsMutedState(muted);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to set mute state';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, onError]);

  const setVolume = useCallback((volume: number) => {
    if (!managerRef.current || !isInitialized) {
      setError('Audio output not initialized');
      return;
    }

    try {
      setError(null);
      managerRef.current.setVolume(volume);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to set volume';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, onError]);

  const stopPlayback = useCallback(() => {
    if (!managerRef.current || !isInitialized) {
      return;
    }

    try {
      managerRef.current.stopPlayback();
      setIsPlaying(false);
      setQueueLength(0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop playback';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, onError]);

  const clearQueue = useCallback(() => {
    if (!managerRef.current || !isInitialized) {
      return;
    }

    try {
      managerRef.current.clearQueue();
      setQueueLength(0);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to clear queue';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, onError]);

  const setOutputDevice = useCallback(async (deviceId: string) => {
    if (!managerRef.current || !isInitialized) {
      setError('Audio output not initialized');
      return;
    }

    try {
      setError(null);
      await managerRef.current.setOutputDevice(deviceId);
      setSelectedOutputDeviceId(deviceId);
      
      console.log('Audio output device changed successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to change output device';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, onError]);

  const cleanup = useCallback(() => {
    if (statusIntervalRef.current) {
      clearInterval(statusIntervalRef.current);
      statusIntervalRef.current = null;
    }

    if (managerRef.current) {
      managerRef.current.cleanup();
    }

    setIsInitialized(false);
    setIsPlaying(false);
    setIsMutedState(false);
    setPlaybackRateState(1.0);
    setQueueLength(0);
    setOutputDevices([]);
    setSelectedOutputDeviceId(null);
    setAudioConfig(null);
    setError(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    isInitialized,
    isPlaying,
    isMuted,
    playbackRate,
    queueLength,
    outputDevices,
    selectedOutputDeviceId,
    audioConfig,
    initialize,
    addAudioChunk,
    setPlaybackRate,
    setMuted,
    setVolume,
    stopPlayback,
    clearQueue,
    setOutputDevice,
    cleanup,
    error
  };
}
