import { AudioConfig } from '@/types/realtime';

interface AudioChunk {
  id: string;
  data: ArrayBuffer;
  timestamp: number;
}

export class AudioOutputManager {
  private audioContext: AudioContext | null = null;
  private gainNode: GainNode | null = null;
  private audioQueue: AudioChunk[] = [];
  private isPlaying = false;
  private isMuted = false;
  private playbackRate = 1.0;
  private currentSource: AudioBufferSourceNode | null = null;
  private audioConfig: AudioConfig = {
    sampleRate: 24000,
    channels: 1,
    bitsPerSample: 16
  };
  private nextPlayTime = 0;
  private bufferDuration = 0.1; // 100ms buffer

  constructor() {
    this.processAudioQueue = this.processAudioQueue.bind(this);
  }

  public async initialize(): Promise<void> {
    try {
      // Create audio context
      this.audioContext = new (window.AudioContext || (window as unknown as { webkitAudioContext: typeof AudioContext }).webkitAudioContext)({
        sampleRate: this.audioConfig.sampleRate
      });

      // Create gain node for volume/mute control
      this.gainNode = this.audioContext.createGain();
      this.gainNode.connect(this.audioContext.destination);

      // Set initial volume
      this.gainNode.gain.value = this.isMuted ? 0 : 1;

      console.log('Audio output initialized successfully');
    } catch (error) {
      console.error('Failed to initialize audio output:', error);
      throw new Error('Audio output initialization failed');
    }
  }

  public addAudioChunk(base64Audio: string, chunkId?: string): void {
    if (!this.audioContext) {
      console.warn('Audio output not initialized');
      return;
    }

    try {
      // Decode base64 to ArrayBuffer
      const binaryString = atob(base64Audio);
      const bytes = new Uint8Array(binaryString.length);
      
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      const audioChunk: AudioChunk = {
        id: chunkId || `chunk_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        data: bytes.buffer,
        timestamp: Date.now()
      };

      this.audioQueue.push(audioChunk);

      // Start processing if not already playing
      if (!this.isPlaying) {
        this.startPlayback();
      }
    } catch (error) {
      console.error('Failed to add audio chunk:', error);
    }
  }

  private async startPlayback(): Promise<void> {
    if (!this.audioContext || this.isPlaying) {
      return;
    }

    this.isPlaying = true;
    this.nextPlayTime = this.audioContext.currentTime;

    // Resume audio context if suspended
    if (this.audioContext.state === 'suspended') {
      await this.audioContext.resume();
    }

    this.processAudioQueue();
  }

  private async processAudioQueue(): Promise<void> {
    if (!this.audioContext || !this.gainNode || !this.isPlaying) {
      return;
    }

    while (this.audioQueue.length > 0 && this.isPlaying) {
      const chunk = this.audioQueue.shift()!;
      
      try {
        // Convert PCM data to AudioBuffer
        const audioBuffer = await this.createAudioBuffer(chunk.data);
        
        if (audioBuffer && this.isPlaying) {
          await this.playAudioBuffer(audioBuffer);
        }
      } catch (error) {
        console.error('Failed to process audio chunk:', error);
      }
    }

    // Stop playback when queue is empty
    if (this.audioQueue.length === 0) {
      this.isPlaying = false;
    }
  }

  private async createAudioBuffer(pcmData: ArrayBuffer): Promise<AudioBuffer | null> {
    if (!this.audioContext) return null;

    try {
      // Convert Int16 PCM to Float32
      const int16Array = new Int16Array(pcmData);
      const float32Array = new Float32Array(int16Array.length);
      
      for (let i = 0; i < int16Array.length; i++) {
        float32Array[i] = int16Array[i] / 0x7FFF;
      }

      // Create AudioBuffer
      const audioBuffer = this.audioContext.createBuffer(
        this.audioConfig.channels,
        float32Array.length,
        this.audioConfig.sampleRate
      );

      // Copy data to buffer
      audioBuffer.getChannelData(0).set(float32Array);

      return audioBuffer;
    } catch (error) {
      console.error('Failed to create audio buffer:', error);
      return null;
    }
  }

  private async playAudioBuffer(audioBuffer: AudioBuffer): Promise<void> {
    if (!this.audioContext || !this.gainNode) return;

    return new Promise((resolve) => {
      // Create source node
      const source = this.audioContext!.createBufferSource();
      source.buffer = audioBuffer;
      source.playbackRate.value = this.playbackRate;
      
      // Connect to gain node
      source.connect(this.gainNode!);

      // Set up completion handler
      source.onended = () => {
        if (this.currentSource === source) {
          this.currentSource = null;
        }
        resolve();
      };

      // Schedule playback
      const startTime = Math.max(this.nextPlayTime, this.audioContext!.currentTime);
      source.start(startTime);
      
      // Update next play time
      this.nextPlayTime = startTime + (audioBuffer.duration / this.playbackRate);
      
      // Track current source for potential cancellation
      this.currentSource = source;
    });
  }

  public setPlaybackRate(rate: number): void {
    if (rate < 0.25 || rate > 4.0) {
      console.warn('Playback rate must be between 0.25 and 4.0');
      return;
    }

    this.playbackRate = rate;

    // Update current source if playing
    if (this.currentSource) {
      this.currentSource.playbackRate.value = rate;
    }

    console.log(`Playback rate set to ${rate}x`);
  }

  public getPlaybackRate(): number {
    return this.playbackRate;
  }

  public setMuted(muted: boolean): void {
    this.isMuted = muted;

    if (this.gainNode) {
      // Smooth transition to avoid audio pops
      const currentTime = this.audioContext!.currentTime;
      this.gainNode.gain.cancelScheduledValues(currentTime);
      this.gainNode.gain.setValueAtTime(this.gainNode.gain.value, currentTime);
      this.gainNode.gain.linearRampToValueAtTime(muted ? 0 : 1, currentTime + 0.05);
    }

    console.log(`Audio ${muted ? 'muted' : 'unmuted'}`);
  }

  public isMutedState(): boolean {
    return this.isMuted;
  }

  public setVolume(volume: number): void {
    if (volume < 0 || volume > 1) {
      console.warn('Volume must be between 0 and 1');
      return;
    }

    if (this.gainNode && !this.isMuted) {
      const currentTime = this.audioContext!.currentTime;
      this.gainNode.gain.cancelScheduledValues(currentTime);
      this.gainNode.gain.setValueAtTime(this.gainNode.gain.value, currentTime);
      this.gainNode.gain.linearRampToValueAtTime(volume, currentTime + 0.05);
    }
  }

  public stopPlayback(): void {
    this.isPlaying = false;

    // Stop current source
    if (this.currentSource) {
      try {
        this.currentSource.stop();
      } catch {
        // Source might already be stopped
      }
      this.currentSource = null;
    }

    // Clear queue
    this.audioQueue = [];
    this.nextPlayTime = 0;

    console.log('Audio playback stopped');
  }

  public clearQueue(): void {
    this.audioQueue = [];
    console.log('Audio queue cleared');
  }

  public getQueueLength(): number {
    return this.audioQueue.length;
  }

  public isActivelyPlaying(): boolean {
    return this.isPlaying && this.currentSource !== null;
  }

  public getAudioConfig(): AudioConfig {
    return { ...this.audioConfig };
  }

  public isInitialized(): boolean {
    return this.audioContext !== null && this.gainNode !== null;
  }

  public async getOutputDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'audiooutput');
    } catch (error) {
      console.error('Failed to enumerate audio output devices:', error);
      return [];
    }
  }

  public async setOutputDevice(deviceId: string): Promise<void> {
    if (!this.audioContext) {
      throw new Error('Audio output not initialized');
    }

    try {
      // Note: setSinkId is not widely supported yet
      if ('setSinkId' in this.audioContext) {
        await (this.audioContext as AudioContext & { setSinkId: (deviceId: string) => Promise<void> }).setSinkId(deviceId);
        console.log('Audio output device changed');
      } else {
        console.warn('setSinkId not supported in this browser');
      }
    } catch (error) {
      console.error('Failed to set output device:', error);
      throw error;
    }
  }

  public cleanup(): void {
    this.stopPlayback();

    if (this.gainNode) {
      this.gainNode.disconnect();
      this.gainNode = null;
    }

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }

    console.log('Audio output cleaned up');
  }
}
