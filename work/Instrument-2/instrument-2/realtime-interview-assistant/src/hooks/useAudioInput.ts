'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { AudioInputManager } from '@/lib/audio-input';
import { AudioConfig } from '@/types/realtime';

interface UseAudioInputOptions {
  autoInitialize?: boolean;
  onAudioData?: (audioData: string) => void;
  onError?: (error: Error) => void;
}

interface UseAudioInputReturn {
  isInitialized: boolean;
  isRecording: boolean;
  audioLevel: number;
  audioDevices: MediaDeviceInfo[];
  selectedDeviceId: string | null;
  audioConfig: AudioConfig | null;
  initialize: () => Promise<void>;
  startRecording: () => void;
  stopRecording: () => void;
  switchDevice: (deviceId: string) => Promise<void>;
  cleanup: () => void;
  error: string | null;
}

export function useAudioInput({
  autoInitialize = false,
  onAudioData,
  onError
}: UseAudioInputOptions = {}): UseAudioInputReturn {
  const [isInitialized, setIsInitialized] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const [audioDevices, setAudioDevices] = useState<MediaDeviceInfo[]>([]);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);
  const [audioConfig, setAudioConfig] = useState<AudioConfig | null>(null);
  const [error, setError] = useState<string | null>(null);

  const managerRef = useRef<AudioInputManager | null>(null);
  const audioLevelIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize manager
  useEffect(() => {
    managerRef.current = new AudioInputManager();

    return () => {
      if (managerRef.current) {
        managerRef.current.cleanup();
      }
    };
  }, []);

  const initialize = useCallback(async () => {
    if (!managerRef.current) return;

    try {
      setError(null);
      await managerRef.current.initialize();
      
      setIsInitialized(true);
      setAudioConfig(managerRef.current.getAudioConfig());

      // Load available audio devices
      const devices = await managerRef.current.getAudioDevices();
      setAudioDevices(devices);
      
      // Set default device (first available)
      if (devices.length > 0) {
        setSelectedDeviceId(devices[0].deviceId);
      }

      console.log('Audio input initialized successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize audio input';
      setError(errorMessage);
      setIsInitialized(false);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [onError]);

  // Auto-initialize if enabled
  useEffect(() => {
    if (autoInitialize && !isInitialized) {
      initialize();
    }
  }, [autoInitialize, isInitialized, initialize]);

  // Update audio level periodically when recording
  useEffect(() => {
    if (isRecording && managerRef.current) {
      audioLevelIntervalRef.current = setInterval(() => {
        if (managerRef.current) {
          setAudioLevel(managerRef.current.getAudioLevel());
        }
      }, 100); // Update every 100ms

      return () => {
        if (audioLevelIntervalRef.current) {
          clearInterval(audioLevelIntervalRef.current);
        }
      };
    } else {
      setAudioLevel(0);
    }
  }, [isRecording]);

  const startRecording = useCallback(() => {
    if (!managerRef.current || !isInitialized) {
      setError('Audio input not initialized');
      return;
    }

    if (isRecording) {
      console.warn('Recording already in progress');
      return;
    }

    try {
      setError(null);
      
      const handleAudioData = (audioData: string) => {
        if (onAudioData) {
          onAudioData(audioData);
        }
      };

      managerRef.current.startRecording(handleAudioData);
      setIsRecording(true);
      
      console.log('Audio recording started');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to start recording';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, isRecording, onAudioData, onError]);

  const stopRecording = useCallback(() => {
    if (!managerRef.current || !isRecording) {
      return;
    }

    try {
      managerRef.current.stopRecording();
      setIsRecording(false);
      setAudioLevel(0);
      
      console.log('Audio recording stopped');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to stop recording';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isRecording, onError]);

  const switchDevice = useCallback(async (deviceId: string) => {
    if (!managerRef.current || !isInitialized) {
      setError('Audio input not initialized');
      return;
    }

    if (isRecording) {
      setError('Cannot switch device while recording');
      return;
    }

    try {
      setError(null);
      await managerRef.current.switchAudioDevice(deviceId);
      setSelectedDeviceId(deviceId);
      
      console.log('Audio device switched successfully');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to switch audio device';
      setError(errorMessage);
      
      if (onError) {
        onError(err instanceof Error ? err : new Error(errorMessage));
      }
    }
  }, [isInitialized, isRecording, onError]);

  const cleanup = useCallback(() => {
    if (audioLevelIntervalRef.current) {
      clearInterval(audioLevelIntervalRef.current);
      audioLevelIntervalRef.current = null;
    }

    if (managerRef.current) {
      managerRef.current.cleanup();
    }

    setIsInitialized(false);
    setIsRecording(false);
    setAudioLevel(0);
    setAudioDevices([]);
    setSelectedDeviceId(null);
    setAudioConfig(null);
    setError(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return cleanup;
  }, [cleanup]);

  return {
    isInitialized,
    isRecording,
    audioLevel,
    audioDevices,
    selectedDeviceId,
    audioConfig,
    initialize,
    startRecording,
    stopRecording,
    switchDevice,
    cleanup,
    error
  };
}
