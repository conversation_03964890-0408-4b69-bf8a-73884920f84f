// AudioWorklet processor for modern audio processing
// This replaces the deprecated ScriptProcessorNode

export const audioWorkletProcessorCode = `
class AudioInputProcessor extends AudioWorkletProcessor {
  constructor() {
    super();
    this.bufferSize = 4096;
    this.buffer = new Float32Array(this.bufferSize);
    this.bufferIndex = 0;
  }

  process(inputs, outputs, parameters) {
    const input = inputs[0];
    
    if (input && input.length > 0) {
      const inputChannel = input[0];
      
      for (let i = 0; i < inputChannel.length; i++) {
        this.buffer[this.bufferIndex] = inputChannel[i];
        this.bufferIndex++;
        
        if (this.bufferIndex >= this.bufferSize) {
          // Send buffer to main thread
          this.port.postMessage({
            type: 'audioData',
            buffer: this.buffer.slice()
          });
          
          // Reset buffer
          this.bufferIndex = 0;
        }
      }
    }
    
    return true;
  }
}

registerProcessor('audio-input-processor', AudioInputProcessor);
`;

export function createAudioWorkletBlob(): string {
  const blob = new Blob([audioWorkletProcessorCode], { type: 'application/javascript' });
  return URL.createObjectURL(blob);
}
